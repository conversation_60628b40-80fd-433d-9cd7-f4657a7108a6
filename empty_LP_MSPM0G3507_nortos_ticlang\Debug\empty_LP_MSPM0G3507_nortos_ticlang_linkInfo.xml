<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -ID:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o empty_LP_MSPM0G3507_nortos_ticlang.out -mempty_LP_MSPM0G3507_nortos_ticlang.map -iD:/TI/CCS/mspm0_sdk_2_05_00_05/source -iD:/TI/CCS_Project/1_empty_LP_MSPM0G3507_nortos_ticlang/empty_LP_MSPM0G3507_nortos_ticlang -iD:/TI/CCS_Project/1_empty_LP_MSPM0G3507_nortos_ticlang/empty_LP_MSPM0G3507_nortos_ticlang/Debug/syscfg -iD:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=empty_LP_MSPM0G3507_nortos_ticlang_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./Drivers/MSPM0/Interrupt.o ./Drivers/MSPM0/clock.o ./OLED/OLED.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=0</command_line>
   <link_time>0x68aff93b</link_time>
   <link_errors>0x0</link_errors>
   <output_file>D:\TI\CCS_Project\1_empty_LP_MSPM0G3507_nortos_ticlang\empty_LP_MSPM0G3507_nortos_ticlang\Debug\empty_LP_MSPM0G3507_nortos_ticlang.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0xdd1</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>D:\TI\CCS_Project\1_empty_LP_MSPM0G3507_nortos_ticlang\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>D:\TI\CCS_Project\1_empty_LP_MSPM0G3507_nortos_ticlang\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>D:\TI\CCS_Project\1_empty_LP_MSPM0G3507_nortos_ticlang\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>D:\TI\CCS_Project\1_empty_LP_MSPM0G3507_nortos_ticlang\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\Drivers\MSPM0\</path>
         <kind>object</kind>
         <file>Interrupt.o</file>
         <name>Interrupt.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>D:\TI\CCS_Project\1_empty_LP_MSPM0G3507_nortos_ticlang\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\Drivers\MSPM0\</path>
         <kind>object</kind>
         <file>clock.o</file>
         <name>clock.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>D:\TI\CCS_Project\1_empty_LP_MSPM0G3507_nortos_ticlang\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\OLED\</path>
         <kind>object</kind>
         <file>OLED.o</file>
         <name>OLED.o</name>
      </input_file>
      <input_file id="fl-13">
         <path>D:\TI\CCS_Project\1_empty_LP_MSPM0G3507_nortos_ticlang\empty_LP_MSPM0G3507_nortos_ticlang\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-14">
         <path>D:\TI\CCS\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-15">
         <path>D:\TI\CCS\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_i2c.o</name>
      </input_file>
      <input_file id="fl-2c">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2d">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-2e">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-d6">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-d7">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-d8">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-d9">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-da">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-db">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-dc">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-99">
         <name>.text.OLED_ShowChar</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x118</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-91">
         <name>.text.OLED_Init</name>
         <load_address>0x1d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d8</run_address>
         <size>0x110</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.text.main</name>
         <load_address>0x2e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2e8</run_address>
         <size>0x108</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.text.OLED_ShowNum</name>
         <load_address>0x3f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3f0</run_address>
         <size>0xf6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-117">
         <name>.text.__aeabi_idiv0</name>
         <load_address>0x4e6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4e6</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-97">
         <name>.text.OLED_ShowChinese</name>
         <load_address>0x4e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4e8</run_address>
         <size>0xa8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.text.OLED_WR_Byte</name>
         <load_address>0x590</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x590</run_address>
         <size>0x98</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.text.__NVIC_SetPriority</name>
         <load_address>0x628</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x628</run_address>
         <size>0x84</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x6ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6ac</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-98">
         <name>.text.OLED_ShowString</name>
         <load_address>0x728</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x728</run_address>
         <size>0x70</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.text.OLED_Clear</name>
         <load_address>0x798</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x798</run_address>
         <size>0x6a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.text.oled_i2c_sda_unlock</name>
         <load_address>0x804</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x804</run_address>
         <size>0x64</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-112">
         <name>.text.DL_I2C_fillControllerTXFIFO</name>
         <load_address>0x868</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x868</run_address>
         <size>0x5e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-110">
         <name>.text.mspm0_i2c_enable</name>
         <load_address>0x8c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8c8</run_address>
         <size>0x5c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.text.SYSCFG_DL_I2C_OLED_init</name>
         <load_address>0x924</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x924</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-115">
         <name>.text.DL_I2C_startControllerTransfer</name>
         <load_address>0x97c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x97c</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.text.SysTick_Config</name>
         <load_address>0x9cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9cc</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0xa1c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa1c</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.text.mspm0_i2c_disable</name>
         <load_address>0xa60</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa60</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0xaa4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xaa4</run_address>
         <size>0x42</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.text.__aeabi_uidivmod</name>
         <load_address>0xae8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xae8</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-133">
         <name>.text.DL_GPIO_initPeripheralInputFunctionFeatures</name>
         <load_address>0xb28</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb28</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.text.DL_GPIO_initPeripheralInputFunctionFeatures</name>
         <load_address>0xb64</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb64</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.text.OLED_Set_Pos</name>
         <load_address>0xba0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xba0</run_address>
         <size>0x3c</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0xbdc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbdc</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-131">
         <name>.text.DL_GPIO_initDigitalInputFeatures</name>
         <load_address>0xc18</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc18</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-db">
         <name>.text.mspm0_delay_ms</name>
         <load_address>0xc4c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc4c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-da">
         <name>.text.oled_pow</name>
         <load_address>0xc7c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc7c</run_address>
         <size>0x30</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0xcac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcac</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-111">
         <name>.text.mspm0_get_clock_ms</name>
         <load_address>0xcd8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcd8</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0xd04</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd04</run_address>
         <size>0x2a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-136">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0xd2e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd2e</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0xd56</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd56</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-108">
         <name>.text.DL_I2C_setControllerRXFIFOThreshold</name>
         <load_address>0xd80</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd80</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-107">
         <name>.text.DL_I2C_setControllerTXFIFOThreshold</name>
         <load_address>0xda8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xda8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-55">
         <name>.text:_c_int00_noargs</name>
         <load_address>0xdd0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xdd0</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-103">
         <name>.text.DL_I2C_setAnalogGlitchFilterPulseWidth</name>
         <load_address>0xdf8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xdf8</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.text.DL_I2C_setClockConfig</name>
         <load_address>0xe1e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe1e</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-134">
         <name>.text.DL_GPIO_enableHiZ</name>
         <load_address>0xe44</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe44</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.text.DL_GPIO_enableHiZ</name>
         <load_address>0xe60</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe60</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.text.DL_I2C_getSDAStatus</name>
         <load_address>0xe7c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe7c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-60">
         <name>.text.DL_Interrupt_getPendingGroup</name>
         <load_address>0xe98</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe98</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.text.DL_SYSCTL_setMCLKDivider</name>
         <load_address>0xeb4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xeb4</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0xed0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xed0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.text.DL_SYSCTL_setULPCLKDivider</name>
         <load_address>0xeec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xeec</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-96">
         <name>.text.Interrupt_Init</name>
         <load_address>0xf08</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf08</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-132">
         <name>.text.DL_GPIO_enableOutput</name>
         <load_address>0xf24</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf24</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0xf3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf3c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-130">
         <name>.text.DL_GPIO_initDigitalOutput</name>
         <load_address>0xf54</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf54</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0xf6c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf6c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0xf84</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf84</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-113">
         <name>.text.DL_I2C_clearInterruptStatus</name>
         <load_address>0xf9c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf9c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-104">
         <name>.text.DL_I2C_enableAnalogGlitchFilter</name>
         <load_address>0xfb4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfb4</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.text.DL_I2C_enableController</name>
         <load_address>0xfcc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfcc</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-109">
         <name>.text.DL_I2C_enableControllerClockStretching</name>
         <load_address>0xfe4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfe4</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-135">
         <name>.text.DL_I2C_enablePower</name>
         <load_address>0xffc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xffc</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.text.DL_I2C_enablePower</name>
         <load_address>0x1014</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1014</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-116">
         <name>.text.DL_I2C_getRawInterruptStatus</name>
         <load_address>0x102c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x102c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.text.DL_I2C_reset</name>
         <load_address>0x1044</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1044</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.text.DL_I2C_reset</name>
         <load_address>0x105c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x105c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-106">
         <name>.text.DL_I2C_setTimerPeriod</name>
         <load_address>0x1074</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1074</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0x108c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x108c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.text.DL_GPIO_readPins</name>
         <load_address>0x10a4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10a4</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x10ba</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10ba</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x10d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10d0</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-114">
         <name>.text.DL_I2C_getControllerStatus</name>
         <load_address>0x10e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10e4</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-105">
         <name>.text.DL_I2C_resetControllerTransfer</name>
         <load_address>0x10f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10f8</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.text.DL_SYSCTL_disableSYSPLL</name>
         <load_address>0x110c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x110c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x1120</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1120</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.text.SysTick_Init</name>
         <load_address>0x1134</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1134</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-80">
         <name>.text:TI_memcpy_small</name>
         <load_address>0x1148</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1148</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x115a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x115a</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.text.DL_SYSCTL_disableHFXT</name>
         <load_address>0x116c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x116c</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.SysTick_Handler</name>
         <load_address>0x117c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x117c</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.text.delay_ms</name>
         <load_address>0x118c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x118c</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x119c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x119c</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0x11a6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11a6</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-45">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x11b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11b0</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.text:abort</name>
         <load_address>0x11b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11b8</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x11be</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11be</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.text.HOSTexit</name>
         <load_address>0x11c2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11c2</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x11c6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11c6</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.text._system_pre_init</name>
         <load_address>0x11ca</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11ca</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-179">
         <name>__TI_handler_table</name>
         <load_address>0x1af0</load_address>
         <readonly>true</readonly>
         <run_address>0x1af0</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-17c">
         <name>.cinit..bss.load</name>
         <load_address>0x1afc</load_address>
         <readonly>true</readonly>
         <run_address>0x1afc</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-17b">
         <name>.cinit..data.load</name>
         <load_address>0x1b04</load_address>
         <readonly>true</readonly>
         <run_address>0x1b04</run_address>
         <size>0x5</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-17a">
         <name>__TI_cinit_table</name>
         <load_address>0x1b0c</load_address>
         <readonly>true</readonly>
         <run_address>0x1b0c</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-d8">
         <name>.rodata.asc2_1608</name>
         <load_address>0x11d0</load_address>
         <readonly>true</readonly>
         <run_address>0x11d0</run_address>
         <size>0x5f0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.rodata.asc2_0806</name>
         <load_address>0x17c0</load_address>
         <readonly>true</readonly>
         <run_address>0x17c0</run_address>
         <size>0x228</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.rodata.Hzk</name>
         <load_address>0x19e8</load_address>
         <readonly>true</readonly>
         <run_address>0x19e8</run_address>
         <size>0xe0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.rodata.str1.9517790425240694019.1</name>
         <load_address>0x1ac8</load_address>
         <readonly>true</readonly>
         <run_address>0x1ac8</run_address>
         <size>0xe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.rodata.str1.254342170260855183.1</name>
         <load_address>0x1ad6</load_address>
         <readonly>true</readonly>
         <run_address>0x1ad6</run_address>
         <size>0xb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.rodata.str1.14685083708502177989.1</name>
         <load_address>0x1ae1</load_address>
         <readonly>true</readonly>
         <run_address>0x1ae1</run_address>
         <size>0x7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.rodata.str1.17669528882079347314.1</name>
         <load_address>0x1ae8</load_address>
         <readonly>true</readonly>
         <run_address>0x1ae8</run_address>
         <size>0x6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.rodata.gI2C_OLEDClockConfig</name>
         <load_address>0x1aee</load_address>
         <readonly>true</readonly>
         <run_address>0x1aee</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-143">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-d1">
         <name>.data.enable_group1_irq</name>
         <load_address>0x20200008</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200008</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.common:tick_ms</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200004</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-11d">
         <name>.common:start_time</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-17e">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xa3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_abbrev</name>
         <load_address>0xa3</load_address>
         <run_address>0xa3</run_address>
         <size>0x187</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_abbrev</name>
         <load_address>0x22a</load_address>
         <run_address>0x22a</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_abbrev</name>
         <load_address>0x297</load_address>
         <run_address>0x297</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_abbrev</name>
         <load_address>0x40f</load_address>
         <run_address>0x40f</run_address>
         <size>0x143</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_abbrev</name>
         <load_address>0x552</load_address>
         <run_address>0x552</run_address>
         <size>0x1ca</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-129">
         <name>.debug_abbrev</name>
         <load_address>0x71c</load_address>
         <run_address>0x71c</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.debug_abbrev</name>
         <load_address>0x77e</load_address>
         <run_address>0x77e</run_address>
         <size>0x1e7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_abbrev</name>
         <load_address>0x965</load_address>
         <run_address>0x965</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_abbrev</name>
         <load_address>0xa14</load_address>
         <run_address>0xa14</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_abbrev</name>
         <load_address>0xb84</load_address>
         <run_address>0xb84</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_abbrev</name>
         <load_address>0xbbd</load_address>
         <run_address>0xbbd</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_abbrev</name>
         <load_address>0xc7f</load_address>
         <run_address>0xc7f</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_abbrev</name>
         <load_address>0xcef</load_address>
         <run_address>0xcef</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_abbrev</name>
         <load_address>0xd7c</load_address>
         <run_address>0xd7c</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.debug_abbrev</name>
         <load_address>0xe14</load_address>
         <run_address>0xe14</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_abbrev</name>
         <load_address>0xe40</load_address>
         <run_address>0xe40</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.debug_abbrev</name>
         <load_address>0xe67</load_address>
         <run_address>0xe67</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-137">
         <name>.debug_abbrev</name>
         <load_address>0xe8e</load_address>
         <run_address>0xe8e</run_address>
         <size>0x59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_abbrev</name>
         <load_address>0xee7</load_address>
         <run_address>0xee7</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-180">
         <name>.debug_abbrev</name>
         <load_address>0xf0c</load_address>
         <run_address>0xf0c</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1c5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_info</name>
         <load_address>0x1c5</load_address>
         <run_address>0x1c5</run_address>
         <size>0x1a3d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x1c02</load_address>
         <run_address>0x1c02</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_info</name>
         <load_address>0x1c82</load_address>
         <run_address>0x1c82</run_address>
         <size>0x486</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_info</name>
         <load_address>0x2108</load_address>
         <run_address>0x2108</run_address>
         <size>0x43b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_info</name>
         <load_address>0x2543</load_address>
         <run_address>0x2543</run_address>
         <size>0x1af3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.debug_info</name>
         <load_address>0x4036</load_address>
         <run_address>0x4036</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-100">
         <name>.debug_info</name>
         <load_address>0x40ab</load_address>
         <run_address>0x40ab</run_address>
         <size>0xcc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x4d6d</load_address>
         <run_address>0x4d6d</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-af">
         <name>.debug_info</name>
         <load_address>0x5190</load_address>
         <run_address>0x5190</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_info</name>
         <load_address>0x58d4</load_address>
         <run_address>0x58d4</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_info</name>
         <load_address>0x591a</load_address>
         <run_address>0x591a</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_info</name>
         <load_address>0x5aac</load_address>
         <run_address>0x5aac</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_info</name>
         <load_address>0x5b72</load_address>
         <run_address>0x5b72</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_info</name>
         <load_address>0x5cee</load_address>
         <run_address>0x5cee</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-df">
         <name>.debug_info</name>
         <load_address>0x5de6</load_address>
         <run_address>0x5de6</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_info</name>
         <load_address>0x5e21</load_address>
         <run_address>0x5e21</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.debug_info</name>
         <load_address>0x5fba</load_address>
         <run_address>0x5fba</run_address>
         <size>0x1bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-118">
         <name>.debug_info</name>
         <load_address>0x6176</load_address>
         <run_address>0x6176</run_address>
         <size>0x85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_info</name>
         <load_address>0x61fb</load_address>
         <run_address>0x61fb</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-17f">
         <name>.debug_info</name>
         <load_address>0x64f5</load_address>
         <run_address>0x64f5</run_address>
         <size>0xd3</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-a2">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1b8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_str</name>
         <load_address>0x1b8</load_address>
         <run_address>0x1b8</run_address>
         <size>0x1709</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_str</name>
         <load_address>0x18c1</load_address>
         <run_address>0x18c1</run_address>
         <size>0x18e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_str</name>
         <load_address>0x1a4f</load_address>
         <run_address>0x1a4f</run_address>
         <size>0x51c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_str</name>
         <load_address>0x1f6b</load_address>
         <run_address>0x1f6b</run_address>
         <size>0x4bb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_str</name>
         <load_address>0x2426</load_address>
         <run_address>0x2426</run_address>
         <size>0xf8f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.debug_str</name>
         <load_address>0x33b5</load_address>
         <run_address>0x33b5</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.debug_str</name>
         <load_address>0x3522</load_address>
         <run_address>0x3522</run_address>
         <size>0x8af</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_str</name>
         <load_address>0x3dd1</load_address>
         <run_address>0x3dd1</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_str</name>
         <load_address>0x3ff6</load_address>
         <run_address>0x3ff6</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_str</name>
         <load_address>0x4325</load_address>
         <run_address>0x4325</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_str</name>
         <load_address>0x441a</load_address>
         <run_address>0x441a</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_str</name>
         <load_address>0x45b5</load_address>
         <run_address>0x45b5</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_str</name>
         <load_address>0x471d</load_address>
         <run_address>0x471d</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.debug_str</name>
         <load_address>0x48f2</load_address>
         <run_address>0x48f2</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.debug_str</name>
         <load_address>0x4a3a</load_address>
         <run_address>0x4a3a</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-138">
         <name>.debug_str</name>
         <load_address>0x4b23</load_address>
         <run_address>0x4b23</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_frame</name>
         <load_address>0x2c</load_address>
         <run_address>0x2c</run_address>
         <size>0x26c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_frame</name>
         <load_address>0x298</load_address>
         <run_address>0x298</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_frame</name>
         <load_address>0x2c8</load_address>
         <run_address>0x2c8</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_frame</name>
         <load_address>0x340</load_address>
         <run_address>0x340</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_frame</name>
         <load_address>0x3cc</load_address>
         <run_address>0x3cc</run_address>
         <size>0x360</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.debug_frame</name>
         <load_address>0x72c</load_address>
         <run_address>0x72c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-102">
         <name>.debug_frame</name>
         <load_address>0x74c</load_address>
         <run_address>0x74c</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_frame</name>
         <load_address>0x878</load_address>
         <run_address>0x878</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.debug_frame</name>
         <load_address>0x908</load_address>
         <run_address>0x908</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_frame</name>
         <load_address>0xa08</load_address>
         <run_address>0xa08</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_frame</name>
         <load_address>0xa28</load_address>
         <run_address>0xa28</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_frame</name>
         <load_address>0xa60</load_address>
         <run_address>0xa60</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_frame</name>
         <load_address>0xa88</load_address>
         <run_address>0xa88</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.debug_frame</name>
         <load_address>0xab8</load_address>
         <run_address>0xab8</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-de">
         <name>.debug_frame</name>
         <load_address>0xae8</load_address>
         <run_address>0xae8</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.debug_frame</name>
         <load_address>0xb08</load_address>
         <run_address>0xb08</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x141</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_line</name>
         <load_address>0x141</load_address>
         <run_address>0x141</run_address>
         <size>0x69d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_line</name>
         <load_address>0x7de</load_address>
         <run_address>0x7de</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_line</name>
         <load_address>0x89a</load_address>
         <run_address>0x89a</run_address>
         <size>0x2d9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_line</name>
         <load_address>0xb73</load_address>
         <run_address>0xb73</run_address>
         <size>0x2cf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_line</name>
         <load_address>0xe42</load_address>
         <run_address>0xe42</run_address>
         <size>0xc22</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.debug_line</name>
         <load_address>0x1a64</load_address>
         <run_address>0x1a64</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.debug_line</name>
         <load_address>0x1bdc</load_address>
         <run_address>0x1bdc</run_address>
         <size>0x682</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_line</name>
         <load_address>0x225e</load_address>
         <run_address>0x225e</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.debug_line</name>
         <load_address>0x243a</load_address>
         <run_address>0x243a</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_line</name>
         <load_address>0x2954</load_address>
         <run_address>0x2954</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_line</name>
         <load_address>0x2992</load_address>
         <run_address>0x2992</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_line</name>
         <load_address>0x2a90</load_address>
         <run_address>0x2a90</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_line</name>
         <load_address>0x2b50</load_address>
         <run_address>0x2b50</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.debug_line</name>
         <load_address>0x2d18</load_address>
         <run_address>0x2d18</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.debug_line</name>
         <load_address>0x2d7f</load_address>
         <run_address>0x2d7f</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_line</name>
         <load_address>0x2dc0</load_address>
         <run_address>0x2dc0</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_line</name>
         <load_address>0x2e64</load_address>
         <run_address>0x2e64</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-119">
         <name>.debug_line</name>
         <load_address>0x2f26</load_address>
         <run_address>0x2f26</run_address>
         <size>0xb5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_line</name>
         <load_address>0x2fdb</load_address>
         <run_address>0x2fdb</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_ranges</name>
         <load_address>0xd8</load_address>
         <run_address>0xd8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_ranges</name>
         <load_address>0xf0</load_address>
         <run_address>0xf0</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_ranges</name>
         <load_address>0x120</load_address>
         <run_address>0x120</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_ranges</name>
         <load_address>0x150</load_address>
         <run_address>0x150</run_address>
         <size>0x118</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-101">
         <name>.debug_ranges</name>
         <load_address>0x268</load_address>
         <run_address>0x268</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_ranges</name>
         <load_address>0x440</load_address>
         <run_address>0x440</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_ranges</name>
         <load_address>0x488</load_address>
         <run_address>0x488</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_ranges</name>
         <load_address>0x4d0</load_address>
         <run_address>0x4d0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_ranges</name>
         <load_address>0x4e8</load_address>
         <run_address>0x4e8</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_ranges</name>
         <load_address>0x538</load_address>
         <run_address>0x538</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.debug_ranges</name>
         <load_address>0x550</load_address>
         <run_address>0x550</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_ranges</name>
         <load_address>0x568</load_address>
         <run_address>0x568</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.debug_loc</name>
         <load_address>0x13</load_address>
         <run_address>0x13</run_address>
         <size>0x352</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_loc</name>
         <load_address>0x365</load_address>
         <run_address>0x365</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.debug_loc</name>
         <load_address>0x43d</load_address>
         <run_address>0x43d</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_loc</name>
         <load_address>0x861</load_address>
         <run_address>0x861</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_loc</name>
         <load_address>0x9cd</load_address>
         <run_address>0x9cd</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_loc</name>
         <load_address>0xa3c</load_address>
         <run_address>0xa3c</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_loc</name>
         <load_address>0xba3</load_address>
         <run_address>0xba3</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-139">
         <name>.debug_loc</name>
         <load_address>0xbc9</load_address>
         <run_address>0xbc9</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x1110</size>
         <contents>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-6a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x1af0</load_address>
         <run_address>0x1af0</run_address>
         <size>0x30</size>
         <contents>
            <object_component_ref idref="oc-179"/>
            <object_component_ref idref="oc-17c"/>
            <object_component_ref idref="oc-17b"/>
            <object_component_ref idref="oc-17a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x11d0</load_address>
         <run_address>0x11d0</run_address>
         <size>0x920</size>
         <contents>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-10b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-143"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200008</run_address>
         <size>0x1</size>
         <contents>
            <object_component_ref idref="oc-d1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-11d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-17e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13a" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13b" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13c" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13d" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13e" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13f" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-141" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-15d" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xf1b</size>
         <contents>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-180"/>
         </contents>
      </logical_group>
      <logical_group id="lg-15f" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x65c8</size>
         <contents>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-17f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-161" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x4cb6</size>
         <contents>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-138"/>
         </contents>
      </logical_group>
      <logical_group id="lg-163" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xb38</size>
         <contents>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-11a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-165" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x307b</size>
         <contents>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-82"/>
         </contents>
      </logical_group>
      <logical_group id="lg-167" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x590</size>
         <contents>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-83"/>
         </contents>
      </logical_group>
      <logical_group id="lg-169" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xbe9</size>
         <contents>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-139"/>
         </contents>
      </logical_group>
      <logical_group id="lg-173" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x68</size>
         <contents>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-81"/>
         </contents>
      </logical_group>
      <logical_group id="lg-17d" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-188" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1b20</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-189" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x9</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-18a" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x1b20</used_space>
         <unused_space>0x1e4e0</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x1110</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x11d0</start_address>
               <size>0x920</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x1af0</start_address>
               <size>0x30</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x1b20</start_address>
               <size>0x1e4e0</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x209</used_space>
         <unused_space>0x7df7</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-13f"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-141"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200008</start_address>
               <size>0x1</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200009</start_address>
               <size>0x7df7</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.bss</name>
            <load_address>0x1afc</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x8</run_size>
            <compression>zero_init</compression>
         </cprec>
         <cprec>
            <name>.data</name>
            <load_address>0x1b04</load_address>
            <load_size>0x5</load_size>
            <run_address>0x20200008</run_address>
            <run_size>0x1</run_size>
            <compression>lzss</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x1b0c</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x1b1c</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x1b1c</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x1af0</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x1afc</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3f">
         <name>main</name>
         <value>0x2e9</value>
         <object_component_ref idref="oc-6e"/>
      </symbol>
      <symbol id="sm-a5">
         <name>SYSCFG_DL_init</name>
         <value>0x1121</value>
         <object_component_ref idref="oc-8a"/>
      </symbol>
      <symbol id="sm-a6">
         <name>SYSCFG_DL_initPower</name>
         <value>0xa1d</value>
         <object_component_ref idref="oc-c3"/>
      </symbol>
      <symbol id="sm-a7">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0xaa5</value>
         <object_component_ref idref="oc-c4"/>
      </symbol>
      <symbol id="sm-a8">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0xd05</value>
         <object_component_ref idref="oc-c5"/>
      </symbol>
      <symbol id="sm-a9">
         <name>SYSCFG_DL_I2C_OLED_init</name>
         <value>0x925</value>
         <object_component_ref idref="oc-c6"/>
      </symbol>
      <symbol id="sm-b4">
         <name>Default_Handler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-b5">
         <name>Reset_Handler</name>
         <value>0x11c7</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-b6">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-b7">
         <name>NMI_Handler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-b8">
         <name>HardFault_Handler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-b9">
         <name>SVC_Handler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-ba">
         <name>PendSV_Handler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-bb">
         <name>GROUP0_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-bc">
         <name>TIMG8_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-bd">
         <name>UART3_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-be">
         <name>ADC0_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-bf">
         <name>ADC1_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c0">
         <name>CANFD0_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c1">
         <name>DAC0_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c2">
         <name>SPI0_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c3">
         <name>SPI1_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c4">
         <name>UART1_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c5">
         <name>UART2_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c6">
         <name>UART0_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c7">
         <name>TIMG0_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c8">
         <name>TIMG6_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c9">
         <name>TIMA0_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-ca">
         <name>TIMA1_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-cb">
         <name>TIMG7_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-cc">
         <name>TIMG12_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-cd">
         <name>I2C0_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-ce">
         <name>I2C1_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-cf">
         <name>AES_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d0">
         <name>RTC_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d1">
         <name>DMA_IRQHandler</name>
         <value>0x11bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-e8">
         <name>Interrupt_Init</name>
         <value>0xf09</value>
         <object_component_ref idref="oc-96"/>
      </symbol>
      <symbol id="sm-e9">
         <name>enable_group1_irq</name>
         <value>0x20200008</value>
         <object_component_ref idref="oc-d1"/>
      </symbol>
      <symbol id="sm-ea">
         <name>SysTick_Handler</name>
         <value>0x117d</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-eb">
         <name>GROUP1_IRQHandler</name>
         <value>0x11a7</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-102">
         <name>mspm0_delay_ms</name>
         <value>0xc4d</value>
         <object_component_ref idref="oc-db"/>
      </symbol>
      <symbol id="sm-103">
         <name>tick_ms</name>
         <value>0x20200004</value>
      </symbol>
      <symbol id="sm-104">
         <name>start_time</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-105">
         <name>mspm0_get_clock_ms</name>
         <value>0xcd9</value>
         <object_component_ref idref="oc-111"/>
      </symbol>
      <symbol id="sm-106">
         <name>SysTick_Init</name>
         <value>0x1135</value>
         <object_component_ref idref="oc-8f"/>
      </symbol>
      <symbol id="sm-16d">
         <name>delay_ms</name>
         <value>0x118d</value>
         <object_component_ref idref="oc-9b"/>
      </symbol>
      <symbol id="sm-16e">
         <name>oled_i2c_sda_unlock</name>
         <value>0x805</value>
         <object_component_ref idref="oc-cc"/>
      </symbol>
      <symbol id="sm-16f">
         <name>OLED_WR_Byte</name>
         <value>0x591</value>
         <object_component_ref idref="oc-cd"/>
      </symbol>
      <symbol id="sm-170">
         <name>OLED_Set_Pos</name>
         <value>0xba1</value>
         <object_component_ref idref="oc-d6"/>
      </symbol>
      <symbol id="sm-171">
         <name>OLED_Clear</name>
         <value>0x799</value>
         <object_component_ref idref="oc-9c"/>
      </symbol>
      <symbol id="sm-172">
         <name>OLED_ShowChar</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-99"/>
      </symbol>
      <symbol id="sm-173">
         <name>asc2_1608</name>
         <value>0x11d0</value>
         <object_component_ref idref="oc-d8"/>
      </symbol>
      <symbol id="sm-174">
         <name>asc2_0806</name>
         <value>0x17c0</value>
         <object_component_ref idref="oc-d9"/>
      </symbol>
      <symbol id="sm-175">
         <name>oled_pow</name>
         <value>0xc7d</value>
         <object_component_ref idref="oc-da"/>
      </symbol>
      <symbol id="sm-176">
         <name>OLED_ShowNum</name>
         <value>0x3f1</value>
         <object_component_ref idref="oc-9a"/>
      </symbol>
      <symbol id="sm-177">
         <name>OLED_ShowString</name>
         <value>0x729</value>
         <object_component_ref idref="oc-98"/>
      </symbol>
      <symbol id="sm-178">
         <name>OLED_ShowChinese</name>
         <value>0x4e9</value>
         <object_component_ref idref="oc-97"/>
      </symbol>
      <symbol id="sm-179">
         <name>Hzk</name>
         <value>0x19e8</value>
         <object_component_ref idref="oc-d7"/>
      </symbol>
      <symbol id="sm-17a">
         <name>OLED_Init</name>
         <value>0x1d9</value>
         <object_component_ref idref="oc-91"/>
      </symbol>
      <symbol id="sm-17b">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-17c">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-17d">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-17e">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-17f">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-180">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-181">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-182">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-183">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-18c">
         <name>DL_Common_delayCycles</name>
         <value>0x119d</value>
         <object_component_ref idref="oc-f1"/>
      </symbol>
      <symbol id="sm-198">
         <name>DL_I2C_setClockConfig</name>
         <value>0xe1f</value>
         <object_component_ref idref="oc-fd"/>
      </symbol>
      <symbol id="sm-199">
         <name>DL_I2C_fillControllerTXFIFO</name>
         <value>0x869</value>
         <object_component_ref idref="oc-112"/>
      </symbol>
      <symbol id="sm-1a4">
         <name>_c_int00_noargs</name>
         <value>0xdd1</value>
         <object_component_ref idref="oc-55"/>
      </symbol>
      <symbol id="sm-1a5">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-1b1">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0xbdd</value>
         <object_component_ref idref="oc-aa"/>
      </symbol>
      <symbol id="sm-1b9">
         <name>_system_pre_init</name>
         <value>0x11cb</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-1c4">
         <name>__TI_zero_init_nomemset</name>
         <value>0x10bb</value>
         <object_component_ref idref="oc-4c"/>
      </symbol>
      <symbol id="sm-1cd">
         <name>__TI_decompress_none</name>
         <value>0x115b</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-1d8">
         <name>__TI_decompress_lzss</name>
         <value>0x6ad</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-1e2">
         <name>abort</name>
         <value>0x11b9</value>
         <object_component_ref idref="oc-a3"/>
      </symbol>
      <symbol id="sm-1ec">
         <name>HOSTexit</name>
         <value>0x11c3</value>
         <object_component_ref idref="oc-dc"/>
      </symbol>
      <symbol id="sm-1ed">
         <name>C$$EXIT</name>
         <value>0x11c2</value>
         <object_component_ref idref="oc-dc"/>
      </symbol>
      <symbol id="sm-1f3">
         <name>__aeabi_memcpy</name>
         <value>0x11b1</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-1f4">
         <name>__aeabi_memcpy4</name>
         <value>0x11b1</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-1f5">
         <name>__aeabi_memcpy8</name>
         <value>0x11b1</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-1fb">
         <name>__aeabi_uidiv</name>
         <value>0xae9</value>
         <object_component_ref idref="oc-d2"/>
      </symbol>
      <symbol id="sm-1fc">
         <name>__aeabi_uidivmod</name>
         <value>0xae9</value>
         <object_component_ref idref="oc-d2"/>
      </symbol>
      <symbol id="sm-206">
         <name>__aeabi_idiv0</name>
         <value>0x4e7</value>
         <object_component_ref idref="oc-117"/>
      </symbol>
      <symbol id="sm-20f">
         <name>TI_memcpy_small</name>
         <value>0x1149</value>
         <object_component_ref idref="oc-80"/>
      </symbol>
      <symbol id="sm-210">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-213">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-214">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
