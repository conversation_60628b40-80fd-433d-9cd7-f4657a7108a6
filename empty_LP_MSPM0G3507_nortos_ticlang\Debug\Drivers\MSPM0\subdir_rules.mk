################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
Drivers/MSPM0/%.o: ../Drivers/MSPM0/%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"D:/TI/CCS_Project/1_empty_LP_MSPM0G3507_nortos_ticlang/empty_LP_MSPM0G3507_nortos_ticlang/Drivers/MSPM0" -I"D:/TI/CCS_Project/1_empty_LP_MSPM0G3507_nortos_ticlang/empty_LP_MSPM0G3507_nortos_ticlang/OLED" -I"D:/TI/CCS_Project/1_empty_LP_MSPM0G3507_nortos_ticlang/empty_LP_MSPM0G3507_nortos_ticlang" -I"D:/TI/CCS_Project/1_empty_LP_MSPM0G3507_nortos_ticlang/empty_LP_MSPM0G3507_nortos_ticlang/Debug" -I"D:/TI/CCS/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include" -I"D:/TI/CCS/mspm0_sdk_2_05_00_05/source" -gdwarf-3 -MMD -MP -MF"Drivers/MSPM0/$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


