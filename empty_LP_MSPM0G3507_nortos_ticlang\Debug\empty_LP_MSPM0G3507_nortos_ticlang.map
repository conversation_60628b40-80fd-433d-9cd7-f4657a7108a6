******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Aug 28 14:37:47 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00000dd1


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00001b20  0001e4e0  R  X
  SRAM                  20200000   00008000  00000209  00007df7  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00001b20   00001b20    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00001110   00001110    r-x .text
  000011d0    000011d0    00000920   00000920    r-- .rodata
  00001af0    00001af0    00000030   00000030    r-- .cinit
20200000    20200000    00000009   00000000    rw-
  20200000    20200000    00000008   00000000    rw- .bss
  20200008    20200008    00000001   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00001110     
                  000000c0    00000118     OLED.o (.text.OLED_ShowChar)
                  000001d8    00000110     OLED.o (.text.OLED_Init)
                  000002e8    00000108     empty.o (.text.main)
                  000003f0    000000f6     OLED.o (.text.OLED_ShowNum)
                  000004e6    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000004e8    000000a8     OLED.o (.text.OLED_ShowChinese)
                  00000590    00000098     OLED.o (.text.OLED_WR_Byte)
                  00000628    00000084     clock.o (.text.__NVIC_SetPriority)
                  000006ac    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00000728    00000070     OLED.o (.text.OLED_ShowString)
                  00000798    0000006a     OLED.o (.text.OLED_Clear)
                  00000802    00000002     --HOLE-- [fill = 0]
                  00000804    00000064     OLED.o (.text.oled_i2c_sda_unlock)
                  00000868    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000008c6    00000002     --HOLE-- [fill = 0]
                  000008c8    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00000924    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  0000097c    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  000009cc    00000050     clock.o (.text.SysTick_Config)
                  00000a1c    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000a60    00000044     OLED.o (.text.mspm0_i2c_disable)
                  00000aa4    00000042     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000ae6    00000002     --HOLE-- [fill = 0]
                  00000ae8    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00000b28    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00000b64    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00000ba0    0000003c     OLED.o (.text.OLED_Set_Pos)
                  00000bdc    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000c18    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00000c4c    00000030     clock.o (.text.mspm0_delay_ms)
                  00000c7c    00000030     OLED.o (.text.oled_pow)
                  00000cac    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00000cd8    0000002c     clock.o (.text.mspm0_get_clock_ms)
                  00000d04    0000002a     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000d2e    00000028     OLED.o (.text.DL_Common_updateReg)
                  00000d56    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00000d7e    00000002     --HOLE-- [fill = 0]
                  00000d80    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00000da8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00000dd0    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00000df8    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00000e1e    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00000e44    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00000e60    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00000e7c    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00000e98    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00000eb4    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setMCLKDivider)
                  00000ed0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00000eec    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00000f08    0000001c     Interrupt.o (.text.Interrupt_Init)
                  00000f24    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00000f3c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00000f54    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00000f6c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00000f84    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00000f9c    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00000fb4    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00000fcc    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00000fe4    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00000ffc    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00001014    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  0000102c    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00001044    00000018     OLED.o (.text.DL_I2C_reset)
                  0000105c    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00001074    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  0000108c    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000010a4    00000016     OLED.o (.text.DL_GPIO_readPins)
                  000010ba    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000010d0    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  000010e4    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  000010f8    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  0000110c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00001120    00000014     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00001134    00000014     clock.o (.text.SysTick_Init)
                  00001148    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  0000115a    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000116c    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  0000117c    00000010     Interrupt.o (.text.SysTick_Handler)
                  0000118c    00000010     OLED.o (.text.delay_ms)
                  0000119c    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000011a6    0000000a     Interrupt.o (.text.GROUP1_IRQHandler)
                  000011b0    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000011b8    00000006     libc.a : exit.c.obj (.text:abort)
                  000011be    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000011c2    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000011c6    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000011ca    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000011ce    00000002     --HOLE-- [fill = 0]

.cinit     0    00001af0    00000030     
                  00001af0    0000000c     (__TI_handler_table)
                  00001afc    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00001b04    00000005     (.cinit..data.load) [load image, compression = lzss]
                  00001b09    00000003     --HOLE-- [fill = 0]
                  00001b0c    00000010     (__TI_cinit_table)
                  00001b1c    00000004     --HOLE-- [fill = 0]

.rodata    0    000011d0    00000920     
                  000011d0    000005f0     OLED.o (.rodata.asc2_1608)
                  000017c0    00000228     OLED.o (.rodata.asc2_0806)
                  000019e8    000000e0     OLED.o (.rodata.Hzk)
                  00001ac8    0000000e     empty.o (.rodata.str1.9517790425240694019.1)
                  00001ad6    0000000b     empty.o (.rodata.str1.254342170260855183.1)
                  00001ae1    00000007     empty.o (.rodata.str1.14685083708502177989.1)
                  00001ae8    00000006     empty.o (.rodata.str1.17669528882079347314.1)
                  00001aee    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000008     UNINITIALIZED
                  20200000    00000004     (.common:start_time)
                  20200004    00000004     (.common:tick_ms)

.data      0    20200008    00000001     UNINITIALIZED
                  20200008    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             886    2         0      
       empty.o                        264    38        0      
       startup_mspm0g350x_ticlang.o   8      192       0      
    +--+------------------------------+------+---------+---------+
       Total:                         1158   232       0      
                                                              
    .\Drivers\MSPM0\
       clock.o                        324    0         8      
       Interrupt.o                    126    0         1      
    +--+------------------------------+------+---------+---------+
       Total:                         450    0         9      
                                                              
    .\OLED\
       OLED.o                         2238   2296      0      
    +--+------------------------------+------+---------+---------+
       Total:                         2238   2296      0      
                                                              
    D:/TI/CCS/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_i2c.o                       132    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         142    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         292    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_uidivmod.S.obj           64     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         74     0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      41        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   4358   2569      521    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00001b0c records: 2, size/record: 8, table size: 16
	.bss: load addr=00001afc, load size=00000008 bytes, run addr=20200000, run size=00000008 bytes, compression=zero_init
	.data: load addr=00001b04, load size=00000005 bytes, run addr=20200008, run size=00000001 bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00001af0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
000011bf  ADC0_IRQHandler               
000011bf  ADC1_IRQHandler               
000011bf  AES_IRQHandler                
000011c2  C$$EXIT                       
000011bf  CANFD0_IRQHandler             
000011bf  DAC0_IRQHandler               
0000119d  DL_Common_delayCycles         
00000869  DL_I2C_fillControllerTXFIFO   
00000e1f  DL_I2C_setClockConfig         
000011bf  DMA_IRQHandler                
000011bf  Default_Handler               
000011bf  GROUP0_IRQHandler             
000011a7  GROUP1_IRQHandler             
000011c3  HOSTexit                      
000011bf  HardFault_Handler             
000019e8  Hzk                           
000011bf  I2C0_IRQHandler               
000011bf  I2C1_IRQHandler               
00000f09  Interrupt_Init                
000011bf  NMI_Handler                   
00000799  OLED_Clear                    
000001d9  OLED_Init                     
00000ba1  OLED_Set_Pos                  
000000c1  OLED_ShowChar                 
000004e9  OLED_ShowChinese              
000003f1  OLED_ShowNum                  
00000729  OLED_ShowString               
00000591  OLED_WR_Byte                  
000011bf  PendSV_Handler                
000011bf  RTC_IRQHandler                
000011c7  Reset_Handler                 
000011bf  SPI0_IRQHandler               
000011bf  SPI1_IRQHandler               
000011bf  SVC_Handler                   
00000aa5  SYSCFG_DL_GPIO_init           
00000925  SYSCFG_DL_I2C_OLED_init       
00000d05  SYSCFG_DL_SYSCTL_init         
00001121  SYSCFG_DL_init                
00000a1d  SYSCFG_DL_initPower           
0000117d  SysTick_Handler               
00001135  SysTick_Init                  
000011bf  TIMA0_IRQHandler              
000011bf  TIMA1_IRQHandler              
000011bf  TIMG0_IRQHandler              
000011bf  TIMG12_IRQHandler             
000011bf  TIMG6_IRQHandler              
000011bf  TIMG7_IRQHandler              
000011bf  TIMG8_IRQHandler              
00001149  TI_memcpy_small               
000011bf  UART0_IRQHandler              
000011bf  UART1_IRQHandler              
000011bf  UART2_IRQHandler              
000011bf  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00001b0c  __TI_CINIT_Base               
00001b1c  __TI_CINIT_Limit              
00001b1c  __TI_CINIT_Warm               
00001af0  __TI_Handler_Table_Base       
00001afc  __TI_Handler_Table_Limit      
00000bdd  __TI_auto_init_nobinit_nopinit
000006ad  __TI_decompress_lzss          
0000115b  __TI_decompress_none          
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
000010bb  __TI_zero_init_nomemset       
000004e7  __aeabi_idiv0                 
000011b1  __aeabi_memcpy                
000011b1  __aeabi_memcpy4               
000011b1  __aeabi_memcpy8               
00000ae9  __aeabi_uidiv                 
00000ae9  __aeabi_uidivmod              
ffffffff  __binit__                     
UNDEFED   __mpu_init                    
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
00000dd1  _c_int00_noargs               
UNDEFED   _system_post_cinit            
000011cb  _system_pre_init              
000011b9  abort                         
000017c0  asc2_0806                     
000011d0  asc2_1608                     
ffffffff  binit                         
0000118d  delay_ms                      
20200008  enable_group1_irq             
00000000  interruptVectors              
000002e9  main                          
00000c4d  mspm0_delay_ms                
00000cd9  mspm0_get_clock_ms            
00000805  oled_i2c_sda_unlock           
00000c7d  oled_pow                      
20200000  start_time                    
20200004  tick_ms                       


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  OLED_ShowChar                 
000001d9  OLED_Init                     
00000200  __STACK_SIZE                  
000002e9  main                          
000003f1  OLED_ShowNum                  
000004e7  __aeabi_idiv0                 
000004e9  OLED_ShowChinese              
00000591  OLED_WR_Byte                  
000006ad  __TI_decompress_lzss          
00000729  OLED_ShowString               
00000799  OLED_Clear                    
00000805  oled_i2c_sda_unlock           
00000869  DL_I2C_fillControllerTXFIFO   
00000925  SYSCFG_DL_I2C_OLED_init       
00000a1d  SYSCFG_DL_initPower           
00000aa5  SYSCFG_DL_GPIO_init           
00000ae9  __aeabi_uidiv                 
00000ae9  __aeabi_uidivmod              
00000ba1  OLED_Set_Pos                  
00000bdd  __TI_auto_init_nobinit_nopinit
00000c4d  mspm0_delay_ms                
00000c7d  oled_pow                      
00000cd9  mspm0_get_clock_ms            
00000d05  SYSCFG_DL_SYSCTL_init         
00000dd1  _c_int00_noargs               
00000e1f  DL_I2C_setClockConfig         
00000f09  Interrupt_Init                
000010bb  __TI_zero_init_nomemset       
00001121  SYSCFG_DL_init                
00001135  SysTick_Init                  
00001149  TI_memcpy_small               
0000115b  __TI_decompress_none          
0000117d  SysTick_Handler               
0000118d  delay_ms                      
0000119d  DL_Common_delayCycles         
000011a7  GROUP1_IRQHandler             
000011b1  __aeabi_memcpy                
000011b1  __aeabi_memcpy4               
000011b1  __aeabi_memcpy8               
000011b9  abort                         
000011bf  ADC0_IRQHandler               
000011bf  ADC1_IRQHandler               
000011bf  AES_IRQHandler                
000011bf  CANFD0_IRQHandler             
000011bf  DAC0_IRQHandler               
000011bf  DMA_IRQHandler                
000011bf  Default_Handler               
000011bf  GROUP0_IRQHandler             
000011bf  HardFault_Handler             
000011bf  I2C0_IRQHandler               
000011bf  I2C1_IRQHandler               
000011bf  NMI_Handler                   
000011bf  PendSV_Handler                
000011bf  RTC_IRQHandler                
000011bf  SPI0_IRQHandler               
000011bf  SPI1_IRQHandler               
000011bf  SVC_Handler                   
000011bf  TIMA0_IRQHandler              
000011bf  TIMA1_IRQHandler              
000011bf  TIMG0_IRQHandler              
000011bf  TIMG12_IRQHandler             
000011bf  TIMG6_IRQHandler              
000011bf  TIMG7_IRQHandler              
000011bf  TIMG8_IRQHandler              
000011bf  UART0_IRQHandler              
000011bf  UART1_IRQHandler              
000011bf  UART2_IRQHandler              
000011bf  UART3_IRQHandler              
000011c2  C$$EXIT                       
000011c3  HOSTexit                      
000011c7  Reset_Handler                 
000011cb  _system_pre_init              
000011d0  asc2_1608                     
000017c0  asc2_0806                     
000019e8  Hzk                           
00001af0  __TI_Handler_Table_Base       
00001afc  __TI_Handler_Table_Limit      
00001b0c  __TI_CINIT_Base               
00001b1c  __TI_CINIT_Limit              
00001b1c  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200000  start_time                    
20200004  tick_ms                       
20200008  enable_group1_irq             
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[107 symbols]
